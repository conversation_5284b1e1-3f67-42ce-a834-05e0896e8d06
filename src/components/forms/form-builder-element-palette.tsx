import { But<PERSON> } from "@/components/ui/button";
import {
  IconAt,
  IconChevronDown,
  IconCircleCheck,
  IconHash,
  IconHeading,
  IconMapPin,
  IconPageBreak,
  IconPhone,
  IconSquareCheck,
  IconStar,
  IconUpload,
  IconUser,
  IconWorld,
} from "@tabler/icons-react";
import { CalendarIcon } from "lucide-react";
import { CgDetailsLess, CgDetailsMore } from "react-icons/cg";

interface ElementPaletteProps {
  onAddElement: (elementType: string) => void;
}

export function FormBuilderElementPalette({
  onAddElement,
}: ElementPaletteProps) {
  return (
    <div className="fixed top-16 right-0 h-full w-[380px] overflow-y-auto border-l border-gray-200 bg-white">
      <div className="px-6 pt-6 pb-24">
        <div className="mb-6">
          <h3 className="text-xl font-semibold text-gray-900">Add Elements</h3>
          <p className="mt-1 text-sm text-gray-500">
            Click to add elements to your form
          </p>
        </div>

        <div className="space-y-8">
          {/* Essentials */}
          <div>
            <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
              Essentials
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("short_answer")}
              >
                <CgDetailsLess
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Short answer
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("long_answer")}
              >
                <CgDetailsMore
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Long answer
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("number")}
              >
                <IconHash
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Number
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("date")}
              >
                <CalendarIcon
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Date
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("single_choice")}
              >
                <IconCircleCheck
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Single choice
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("multiple_choice")}
              >
                <IconSquareCheck
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Multiple choice
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("dropdown")}
              >
                <IconChevronDown
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Dropdown
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("heading")}
              >
                <IconHeading
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Heading
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative col-span-2 flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                onClick={() => onAddElement("page")}
              >
                <IconPageBreak
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-blue-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                  Page Break
                </span>
              </Button>
            </div>
          </div>

          {/* Contact Details */}
          <div>
            <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
              Contact Details
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                onClick={() => onAddElement("name")}
              >
                <IconUser
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-green-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                  Full name
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                onClick={() => onAddElement("email")}
              >
                <IconAt
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-green-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                  Email
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                onClick={() => onAddElement("phone")}
              >
                <IconPhone
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-green-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                  Phone
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                onClick={() => onAddElement("address")}
              >
                <IconMapPin
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-green-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                  Address
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative col-span-2 flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                onClick={() => onAddElement("website")}
              >
                <IconWorld
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-green-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                  Website
                </span>
              </Button>
            </div>
          </div>

          {/* Other */}
          <div>
            <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
              Other
            </h4>
            <div className="grid grid-cols-2 gap-3">
              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50 hover:shadow-md"
                onClick={() => onAddElement("rating")}
              >
                <IconStar
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-purple-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-purple-700">
                  Rating
                </span>
              </Button>

              <Button
                variant="outline"
                className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50 hover:shadow-md"
                onClick={() => onAddElement("file_upload")}
              >
                <IconUpload
                  size={24}
                  className="text-gray-600 transition-colors group-hover:text-purple-600"
                />
                <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-purple-700">
                  File upload
                </span>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
