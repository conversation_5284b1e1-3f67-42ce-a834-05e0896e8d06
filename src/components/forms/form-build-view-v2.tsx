"use client";

import { ELEMENT_COMPONENTS } from "@/components/forms/elements/element-map";
import { FormBuilderNavbar } from "@/components/forms/form-builder-navbar";
import { FormBuilderPreview } from "@/components/forms/form-builder-preview";
import { FormColorPicker } from "@/components/forms/form-color-picker";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ImageUploader } from "@/components/ui/image-uploader";
import { Input } from "@/components/ui/input";
import { Loader } from "@/components/ui/loader";
import BubbleEditor from "@/components/ui/rich-text-editor-bubble";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { env } from "@/env";
import { useDialog } from "@/hooks/use-dialog";
import { nanoid } from "@/libs/nanoid";
import { useFormById } from "@/queries/form.queries";
import { useOrgById } from "@/queries/org.queries";
import {
  useFileDeleteMutation,
  useFileUploadUrlMutation,
} from "@/queries/storage.queries";
import { useFormStore } from "@/stores/form.store";
import type {
  FormBorderStyle,
  FormByIdOutput,
  FormField,
  FormPageMode,
} from "@/types/form.types";
import { IMAGE_MIME_TYPE } from "@/types/utility.types";
import { compressImage } from "@/utils/compress-image";
import { type ImageMimeType } from "@/utils/constants";
import { type Feature, hasFeatureAccess } from "@/utils/has-feature-access";
import { cn } from "@/utils/tailwind-helpers";
import { move } from "@dnd-kit/helpers";
import { DragDropProvider } from "@dnd-kit/react";
import { RadioGroup } from "@headlessui/react";
import { useScrollIntoView } from "@mantine/hooks";
import {
  IconArrowLeft,
  IconAt,
  IconBrowser,
  IconCheck,
  IconChevronDown,
  IconCircleCheck,
  IconHash,
  IconHeading,
  IconMapPin,
  IconPageBreak,
  IconPhone,
  IconPlus,
  IconSquareCheck,
  IconStar,
  IconUpload,
  IconUser,
  IconWorld,
  IconX,
} from "@tabler/icons-react";
import { CalendarIcon } from "lucide-react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import { isEmpty } from "radash";
import { useEffect } from "react";
import { type ColorResult } from "react-color";
import { CgDetailsLess, CgDetailsMore } from "react-icons/cg";
import TextareaAutosize from "react-textarea-autosize";

interface Props {
  formId: string;
  orgId: string;
}

export function FormBuildViewV2({ formId, orgId }: Props) {
  const searchParams = useSearchParams();
  const [logoUploaderOpen, logoUploaderHandler] = useDialog();
  const [headerUploaderOpen, headerUploaderHandler] = useDialog();
  const { form, setForm, selectedId, select, addField } = useFormStore();
  const { scrollIntoView, targetRef } = useScrollIntoView<HTMLDivElement>({
    offset: 60,
  });

  const deleteFileMutation = useFileDeleteMutation();
  const uploadUrlMutation = useFileUploadUrlMutation();

  const { data: formData, isLoading: isFormLoading } = useFormById(
    { id: formId, orgId },
    {
      refetchOnWindowFocus: false,
    },
  );

  const org = useOrgById(orgId);

  const mode = searchParams.get("mode") || "builder";

  useEffect(() => {
    setForm(formData as FormByIdOutput);
  }, [formData, setForm]);

  const updateFormHeaderTitle = (
    e: React.SyntheticEvent<HTMLTextAreaElement>,
  ) => {
    const { value } = e.currentTarget;
    setForm({ ...(form as FormByIdOutput), headerTitle: value });
  };

  const updateFormHeaderDescription = (content: string) => {
    setForm({ ...(form as FormByIdOutput), headerDescription: content });
  };

  // Style editor functions
  const updateFormPageMode = (pageMode: FormPageMode) => {
    setForm({ ...(form as FormByIdOutput), pageMode });
  };

  const updateBackgroundColor = (color: ColorResult) => {
    setForm({ ...(form as FormByIdOutput), backgroundColor: color.hex });
  };

  const updateTextColor = (color: ColorResult) => {
    setForm({ ...(form as FormByIdOutput), textColor: color.hex });
  };

  const updateButtonBackgroundColor = (color: ColorResult) => {
    setForm({ ...(form as FormByIdOutput), buttonBackgroundColor: color.hex });
  };

  const updateButtonTextColor = (color: ColorResult) => {
    setForm({ ...(form as FormByIdOutput), buttonTextColor: color.hex });
  };

  const updateAccentColor = (color: ColorResult) => {
    setForm({ ...(form as FormByIdOutput), accentColor: color.hex });
  };

  const updateButtonBorderStyle = (borderStyle: FormBorderStyle) => {
    setForm({ ...(form as FormByIdOutput), buttonBorderStyle: borderStyle });
  };

  const updateInputBorderStyle = (borderStyle: FormBorderStyle) => {
    setForm({ ...(form as FormByIdOutput), inputBorderStyle: borderStyle });
  };

  // Success page handlers
  const handleTPHeaderChange = (e: React.FormEvent<HTMLInputElement>) => {
    const { value } = e.currentTarget;
    setForm({ ...(form as FormByIdOutput), tpHeader: value });
  };

  const handleTPMessageChange = (e: React.FormEvent<HTMLTextAreaElement>) => {
    const { value } = e.currentTarget;
    setForm({ ...(form as FormByIdOutput), tpMessage: value });
  };

  const handleTPButtonTextChange = (e: React.FormEvent<HTMLInputElement>) => {
    const { value } = e.currentTarget;
    setForm({ ...(form as FormByIdOutput), tpButtonText: value });
  };

  const handleTPButtonUrlChange = (e: React.FormEvent<HTMLInputElement>) => {
    const { value } = e.currentTarget;
    setForm({ ...(form as FormByIdOutput), tpButtonUrl: value });
  };

  const deleteImage = async (image: string | null | undefined) => {
    if (!image) return;

    if (!image.startsWith(env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL)) return;

    const fileKey = image.replace(
      `${env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL}/`,
      "",
    );

    try {
      return await deleteFileMutation.mutateAsync({ fileKey });
    } catch (error) {
      console.log(error);
    }
  };

  const onFileUpload = async (file: File, imageKey: "headerImage" | "logo") => {
    let compressedFile = file;
    if (IMAGE_MIME_TYPE.includes(compressedFile.type as ImageMimeType)) {
      compressedFile = await compressImage(file);
    }
    const fileKey = `uploads/${orgId}/forms/${form?.id}/${nanoid()}-${compressedFile.name}`;
    const { uploadUrl } = await uploadUrlMutation.mutateAsync({
      fileKey,
    });
    if (uploadUrl) {
      await fetch(uploadUrl, {
        method: "PUT",
        headers: { "Content-Type": compressedFile.type },
        body: compressedFile,
      });
      setForm({
        ...(form as FormByIdOutput),
        [imageKey]: `${env.NEXT_PUBLIC_AWS_PUBLIC_BUCKET_URL}/${fileKey}`,
      });
    }
  };

  const onUrlUpload = async (url: string, imageKey: "logo" | "headerImage") => {
    setForm({ ...(form as FormByIdOutput), [imageKey]: url });
  };

  async function updateImageUrl(
    imageUrl: string,
    imageKey: "logo" | "headerImage",
  ) {
    if (imageUrl === "") {
      // Delete the current image when clearing
      const currentImage = imageKey === "logo" ? form?.logo : form?.headerImage;
      await deleteImage(currentImage);
    }
    setForm({ ...(form as FormByIdOutput), [imageKey]: imageUrl });
  }

  // Simplified element addition using store actions and element defaults
  const addElement = (elementType: keyof typeof ELEMENT_COMPONENTS) => {
    if (!form) return;

    // Create a simple mapping for element defaults to avoid import issues
    const createDefaultField = (subtype: string): FormField => {
      const baseField: FormField = {
        id: nanoid(),
        type: "text",
        subtype,
        label: "Untitled Question",
        description: "",
        required: false,
        showDescription: false,
        options: [],
      };

      // Customize based on element type
      switch (subtype) {
        case "heading":
          return { ...baseField, label: "Section heading" };
        case "email":
          return {
            ...baseField,
            type: "email",
            label: "Email",
            required: true,
          };
        case "phone":
          return { ...baseField, type: "tel", label: "Phone number" };
        case "number":
          return { ...baseField, type: "number" };
        case "date":
          return { ...baseField, type: "date" };
        case "file_upload":
          return { ...baseField, type: "file" };
        case "multiple_choice":
        case "single_choice":
        case "dropdown":
          return {
            ...baseField,
            options: [
              { id: nanoid(), value: "Option 1" },
              { id: nanoid(), value: "Option 2" },
              { id: nanoid(), value: "Option 3" },
            ],
          };
        case "rating":
          return { ...baseField, ratingCount: 5 };
        case "page":
          const pages =
            form?.fields.filter((field) => field.subtype === "page") || [];
          return {
            ...baseField,
            type: "fb-page-break",
            label: `Page ${pages?.length === 0 ? 2 : pages?.length + 2}`,
          };
        default:
          return baseField;
      }
    };

    // Create new field
    const newField = createDefaultField(elementType);

    // Calculate insert position
    let insertIndex: number;
    if (selectedId) {
      insertIndex = form.fields.findIndex((el) => el.id === selectedId) + 1;
    } else {
      insertIndex = form.fields.length;
    }

    // Add field using store action
    addField(newField, insertIndex);

    // Select the new field
    select(newField.id);

    // Scroll to view if added at the end
    if (insertIndex === form.fields.length) {
      scrollIntoView();
    }
  };

  const addHeadingElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "heading"),
      });
    }
  };
  const addShortAnswerElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "short_answer"),
      });
    }
  };
  const addLongAnswerElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "long_answer"),
      });
    }
  };
  const addEmailElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "email"),
      });
    }
  };
  const addNameElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "name"),
      });
    }
  };
  const addAddressElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "address"),
      });
    }
  };
  const addWebsiteElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "website"),
      });
    }
  };
  const addNumberElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "number"),
      });
    }
  };
  const addPhoneNumberElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "phone"),
      });
    }
  };
  const addDateElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "date"),
      });
    }
  };
  const addSingleChoiceElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "single_choice"),
      });
    }
  };
  const addMultipleChoiceElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "multiple_choice"),
      });
    }
  };
  const addDropdownElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "dropdown"),
      });
    }
  };
  const addRatingElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "rating"),
      });
    }
  };
  const addFileUploadElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "file_upload"),
      });
    }
  };
  const addPageElement = () => {
    if (form) {
      return setForm({
        ...form,
        fields: insertElement(form.fields, "page"),
      });
    }
  };

  const hasAccess = (feature: Feature) => {
    return hasFeatureAccess(org.data?.stripePlan, feature);
  };

  if (isFormLoading || !form) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader />
      </div>
    );
  }

  return (
    <div>
      <div className="hidden md:block">
        <FormBuilderNavbar
          formId={formId}
          orgId={orgId}
          formName={form?.name}
        />
      </div>
      <div className="flex min-h-screen flex-col items-center justify-center p-4 text-center md:hidden">
        <div className="w-full max-w-md rounded-xl border border-gray-200 bg-white p-8 shadow-lg">
          <div className="mx-auto mb-6 flex h-16 w-16 items-center justify-center rounded-full bg-gray-100">
            <IconBrowser size={32} className="text-gray-900" />
          </div>
          <h2 className="mb-3 text-xl font-semibold text-gray-900">
            Desktop Required
          </h2>
          <p className="mb-8 leading-relaxed text-gray-600">
            The form builder is not yet optimized for mobile devices. Please use
            a desktop browser to build your form for the best experience.
          </p>
          <Link
            href={`/dashboard/${orgId}/forms/${formId}`}
            className="inline-flex w-full items-center justify-center rounded-lg bg-gray-900 px-4 py-3 text-sm font-medium text-white transition-colors duration-200 hover:bg-gray-800"
          >
            <IconArrowLeft size={16} className="mr-2" />
            Back to Form
          </Link>
        </div>
      </div>
      <div className="hidden w-full md:block">
        <div className="mx-auto">
          {mode === "builder" && (
            <div className="flex">
              <div className="mx-auto flex-1 py-28 pr-[380px]">
                <div className="mx-auto max-w-3xl space-y-4 px-5">
                  {/* Form Heading Title and Description */}
                  <Card className="w-full border-gray-300">
                    <div className="w-full p-5">
                      <div className="flex w-full flex-col">
                        <TextareaAutosize
                          value={form?.headerTitle || ""}
                          className="resize-none border-none text-3xl font-semibold focus:ring-0"
                          placeholder="Form title"
                          onChange={updateFormHeaderTitle}
                          rows={1}
                        />
                        <BubbleEditor
                          onContentUpdate={updateFormHeaderDescription}
                          defaultContent={form.headerDescription}
                          inputClassName="py-[8px] px-[12px] prose max-w-none prose-sm sm:prose prose-li:marker:text-gray-900 prose-ul:marker:text-gray-900 prose-p:m-0 prose-headings:m-0 prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline"
                          placeholder="Form description (optional)"
                        />
                      </div>
                    </div>
                  </Card>
                  {!isEmpty(form?.fields) && (
                    <DragDropProvider
                      onDragEnd={(event) => {
                        const updatedFields = move(form.fields, event);
                        setForm({
                          ...form,
                          fields: updatedFields,
                        });
                      }}
                    >
                      <div className="mt-4 space-y-4">
                        {form?.fields?.map((field, index) => {
                          const ElementComponent =
                            ELEMENT_COMPONENTS[
                              field.subtype as keyof typeof ELEMENT_COMPONENTS
                            ];

                          if (!ElementComponent) {
                            return (
                              <div
                                key={field.id}
                                className="rounded-lg border border-red-200 bg-red-50 p-4"
                              >
                                <p className="text-sm text-red-600">
                                  Element type &quot;{field.subtype}&quot; not
                                  found in element mapping.
                                </p>
                              </div>
                            );
                          }

                          // Check if this is a refactored element (uses new simplified props)
                          const isRefactoredElement = [
                            "heading",
                            "short_answer",
                            "email",
                            "long_answer",
                            "number",
                          ].includes(field.subtype);

                          if (isRefactoredElement) {
                            // New simplified props for refactored elements
                            const RefactoredComponent =
                              ElementComponent as React.ComponentType<{
                                field: FormField;
                                index?: number;
                              }>;
                            return (
                              <RefactoredComponent
                                key={field.id}
                                field={field}
                                index={index}
                              />
                            );
                          } else {
                            // Old props for non-refactored elements
                            const LegacyComponent =
                              ElementComponent as React.ComponentType<{
                                element: FormField;
                                index?: number;
                                selectedId?: string;
                                setSelectedId: (fieldId: string) => void;
                                setForm: (form: FormByIdOutput) => void;
                                form: FormByIdOutput;
                              }>;
                            return (
                              <LegacyComponent
                                key={field.id}
                                element={field}
                                index={index}
                                selectedId={selectedId}
                                setSelectedId={setSelectedId}
                                setForm={setForm}
                                form={form as FormByIdOutput}
                              />
                            );
                          }
                        })}
                      </div>
                      <div ref={targetRef}></div>
                    </DragDropProvider>
                  )}

                  {isEmpty(form?.fields) && (
                    <div className="flex flex-col items-center justify-center px-8 py-24">
                      <div className="mb-6 rounded-full bg-linear-to-br from-blue-50 to-indigo-100 p-6">
                        <div className="rounded-full bg-linear-to-br from-blue-100 to-indigo-200 p-4">
                          <IconPlus size={32} className="text-blue-600" />
                        </div>
                      </div>
                      <h3 className="mb-2 text-xl font-semibold text-gray-900">
                        Start building your form
                      </h3>
                      <p className="mb-8 max-w-md text-center text-gray-500">
                        Add your first element to get started. Choose from text
                        fields, multiple choice questions, and more from the
                        panel on the right.
                      </p>
                      <div className="flex flex-col gap-3 sm:flex-row">
                        <button
                          onClick={addShortAnswerElement}
                          className="inline-flex items-center rounded-lg border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 transition-colors hover:border-blue-300 hover:bg-blue-100"
                        >
                          <CgDetailsLess size={16} className="mr-2" />
                          Add Short Answer
                        </button>
                        <button
                          onClick={addEmailElement}
                          className="inline-flex items-center rounded-lg border border-green-200 bg-green-50 px-4 py-2 text-sm font-medium text-green-700 transition-colors hover:border-green-300 hover:bg-green-100"
                        >
                          <IconAt size={16} className="mr-2" />
                          Add Email Field
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Floating Element Picker */}
              <div className="fixed top-16 right-0 h-full w-[380px] overflow-y-auto border-l border-gray-200 bg-white">
                <div className="px-6 pt-6 pb-24">
                  <div className="mb-6">
                    <h3 className="text-xl font-semibold text-gray-900">
                      Add Elements
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Click to add elements to your form
                    </p>
                  </div>

                  <div className="space-y-8">
                    {/* Essentials */}
                    <div>
                      <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                        Essentials
                      </h4>
                      <div className="grid grid-cols-2 gap-3">
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addShortAnswerElement}
                        >
                          <CgDetailsLess
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Short answer
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addLongAnswerElement}
                        >
                          <CgDetailsMore
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Long answer
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addNumberElement}
                        >
                          <IconHash
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Number
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addDateElement}
                        >
                          <CalendarIcon
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Date
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addSingleChoiceElement}
                        >
                          <IconCircleCheck
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Single choice
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addMultipleChoiceElement}
                        >
                          <IconSquareCheck
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Multiple choice
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addDropdownElement}
                        >
                          <IconChevronDown
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Dropdown
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addHeadingElement}
                        >
                          <IconHeading
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Heading
                          </span>
                        </button>
                        <button
                          className="group relative col-span-2 flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-blue-300 hover:bg-blue-50 hover:shadow-md"
                          onClick={addPageElement}
                        >
                          <IconPageBreak
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-blue-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-blue-700">
                            Page Break
                          </span>
                        </button>
                      </div>
                    </div>

                    {/* Contact Details */}
                    <div>
                      <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                        Contact Details
                      </h4>
                      <div className="grid grid-cols-2 gap-3">
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                          onClick={addNameElement}
                        >
                          <IconUser
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-green-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                            Full name
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                          onClick={addEmailElement}
                        >
                          <IconAt
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-green-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                            Email
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                          onClick={addPhoneNumberElement}
                        >
                          <IconPhone
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-green-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                            Phone
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                          onClick={addAddressElement}
                        >
                          <IconMapPin
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-green-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                            Address
                          </span>
                        </button>
                        <button
                          className="group relative col-span-2 flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-green-300 hover:bg-green-50 hover:shadow-md"
                          onClick={addWebsiteElement}
                        >
                          <IconWorld
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-green-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-green-700">
                            Website
                          </span>
                        </button>
                      </div>
                    </div>

                    {/* Other */}
                    <div>
                      <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                        Other
                      </h4>
                      <div className="grid grid-cols-2 gap-3">
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50 hover:shadow-md"
                          onClick={addRatingElement}
                        >
                          <IconStar
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-purple-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-purple-700">
                            Rating
                          </span>
                        </button>
                        <button
                          className="group relative flex h-20 cursor-pointer flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-4 transition-all duration-200 hover:border-purple-300 hover:bg-purple-50 hover:shadow-md"
                          onClick={addFileUploadElement}
                        >
                          <IconUpload
                            size={24}
                            className="text-gray-600 transition-colors group-hover:text-purple-600"
                          />
                          <span className="mt-2 text-xs font-medium text-gray-700 group-hover:text-purple-700">
                            File upload
                          </span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {mode === "preview" && (
            <div className="flex">
              <div
                className={cn(
                  "mx-auto flex-1 pr-[380px]",
                  form?.pageMode === "compact" && "pt-20",
                  form?.pageMode === "full" && "pt-16",
                )}
              >
                <FormBuilderPreview form={form as FormByIdOutput} />
              </div>

              {/* Style Editor Sidebar */}
              <div className="fixed top-16 right-0 h-full w-[380px] overflow-y-auto border-l border-gray-200 bg-white">
                <div className="space-y-6 p-6 pb-24">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      Customize Form
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Customize the look and feel of your form.
                    </p>
                  </div>
                  {/* Header Image Section */}
                  <div>
                    <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                      Header image
                    </h4>
                    <div className="mt-2">
                      {form?.headerImage ? (
                        <Card className="p-1">
                          <div className="flex w-full items-center justify-between">
                            <div className="w-[90%] pl-1">
                              <Link
                                href={form?.headerImage}
                                className="hover:underline hover:underline-offset-4"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <p className="truncate text-sm">
                                  {form?.headerImage}
                                </p>
                              </Link>
                            </div>
                            <div>
                              <Button
                                size="icon"
                                variant="secondary"
                                className="h-7 w-7"
                                onClick={() =>
                                  updateImageUrl("", "headerImage")
                                }
                              >
                                <IconX size={16} />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ) : (
                        <Button
                          variant="secondary"
                          leftIcon={<IconPlus size={16} />}
                          onClick={headerUploaderHandler.open}
                        >
                          Add header image
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Logo Section */}
                  <div>
                    <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                      Logo
                    </h4>
                    <div className="mt-2">
                      {form?.logo ? (
                        <Card className="p-1">
                          <div className="flex w-full items-center justify-between">
                            <div className="w-[90%] pl-1">
                              <Link
                                href={form?.logo}
                                className="hover:underline hover:underline-offset-4"
                                target="_blank"
                                rel="noopener noreferrer"
                              >
                                <p className="truncate text-sm">{form?.logo}</p>
                              </Link>
                            </div>
                            <div>
                              <Button
                                size="icon"
                                variant="secondary"
                                className="h-7 w-7"
                                onClick={() => updateImageUrl("", "logo")}
                              >
                                <IconX size={16} />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      ) : (
                        <Button
                          variant="secondary"
                          leftIcon={<IconPlus size={16} />}
                          onClick={logoUploaderHandler.open}
                        >
                          Add logo
                        </Button>
                      )}
                    </div>
                  </div>

                  {/* Layout Section */}
                  <div>
                    <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                      Layout
                    </h4>
                    <div className="mt-2">
                      <RadioGroup
                        value={form?.pageMode}
                        onChange={(value: FormPageMode) =>
                          updateFormPageMode(value)
                        }
                      >
                        <div className="grid grid-cols-2 gap-y-6 sm:gap-x-4">
                          <RadioGroup.Option
                            value={"compact"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative flex cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {({ checked }) => (
                              <>
                                <span className="flex flex-1">
                                  <span className="flex flex-col">
                                    <RadioGroup.Label
                                      as="span"
                                      className="block text-sm text-gray-900"
                                    >
                                      Compact
                                    </RadioGroup.Label>
                                  </span>
                                </span>
                                <IconCheck
                                  className={cn(
                                    !checked ? "invisible" : "",
                                    "h-4 w-4 text-primary",
                                  )}
                                  aria-hidden="true"
                                />
                              </>
                            )}
                          </RadioGroup.Option>
                          <RadioGroup.Option
                            value={"full"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative flex cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {({ checked }) => (
                              <>
                                <span className="flex flex-1">
                                  <span className="flex flex-col">
                                    <RadioGroup.Label
                                      as="span"
                                      className="block text-sm text-gray-900"
                                    >
                                      Full page
                                    </RadioGroup.Label>
                                  </span>
                                </span>
                                <IconCheck
                                  className={cn(
                                    !checked ? "invisible" : "",
                                    "h-4 w-4 text-primary",
                                  )}
                                  aria-hidden="true"
                                />
                              </>
                            )}
                          </RadioGroup.Option>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>

                  {/* Colors Section */}
                  <div>
                    <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                      Colors
                    </h4>
                    <div className="mt-2">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <p className="text-gray-600">Background</p>
                          <FormColorPicker
                            color={form?.backgroundColor?.toLowerCase() || ""}
                            onColorChange={updateBackgroundColor}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-gray-600">Text</p>
                          <FormColorPicker
                            color={form?.textColor?.toLowerCase() || ""}
                            onColorChange={updateTextColor}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-gray-600">Button background</p>
                          <FormColorPicker
                            color={
                              form?.buttonBackgroundColor?.toLowerCase() || ""
                            }
                            onColorChange={updateButtonBackgroundColor}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-gray-600">Button text</p>
                          <FormColorPicker
                            color={form?.buttonTextColor?.toLowerCase() || ""}
                            onColorChange={updateButtonTextColor}
                          />
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-gray-600">Accent</p>
                          <FormColorPicker
                            color={form?.accentColor?.toLowerCase() || ""}
                            onColorChange={updateAccentColor}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Button Style Section */}
                  <div>
                    <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                      Button style
                    </h4>
                    <div className="mt-2">
                      <RadioGroup
                        value={form?.buttonBorderStyle}
                        onChange={(value: FormBorderStyle) =>
                          updateButtonBorderStyle(value)
                        }
                      >
                        <div className="grid grid-cols-3 gap-y-6 sm:gap-x-4">
                          <RadioGroup.Option
                            value={"flat"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {() => (
                              <>
                                <span className="flex flex-1">
                                  <Skeleton
                                    className="h-6 w-full rounded-none"
                                    animatePulse={false}
                                  />
                                </span>
                              </>
                            )}
                          </RadioGroup.Option>
                          <RadioGroup.Option
                            value={"rounded"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {() => (
                              <>
                                <span className="flex flex-1">
                                  <Skeleton
                                    className="h-6 w-full rounded-md"
                                    animatePulse={false}
                                  />
                                </span>
                              </>
                            )}
                          </RadioGroup.Option>
                          <RadioGroup.Option
                            value={"full"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {() => (
                              <>
                                <span className="flex flex-1">
                                  <Skeleton
                                    className="h-6 w-full rounded-full"
                                    animatePulse={false}
                                  />
                                </span>
                              </>
                            )}
                          </RadioGroup.Option>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>

                  {/* Input Style Section */}
                  <div>
                    <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                      Input style
                    </h4>
                    <div className="mt-2">
                      <RadioGroup
                        value={form?.inputBorderStyle}
                        onChange={(value: FormBorderStyle) =>
                          updateInputBorderStyle(value)
                        }
                      >
                        <div className="grid grid-cols-3 gap-y-6 sm:gap-x-4">
                          <RadioGroup.Option
                            value={"flat"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {() => (
                              <>
                                <span className="flex flex-1">
                                  <Skeleton
                                    className="h-6 w-full rounded-none"
                                    animatePulse={false}
                                  />
                                </span>
                              </>
                            )}
                          </RadioGroup.Option>
                          <RadioGroup.Option
                            value={"rounded"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {() => (
                              <>
                                <span className="flex flex-1">
                                  <Skeleton
                                    className="h-6 w-full rounded-md"
                                    animatePulse={false}
                                  />
                                </span>
                              </>
                            )}
                          </RadioGroup.Option>
                          <RadioGroup.Option
                            value={"full"}
                            className={({ checked }) =>
                              cn(
                                checked
                                  ? "border-primary ring-1 ring-primary"
                                  : "border-gray-300",
                                "relative cursor-pointer items-center rounded-lg border bg-white p-3 shadow-xs focus:outline-hidden",
                              )
                            }
                          >
                            {() => (
                              <>
                                <span className="flex flex-1">
                                  <Skeleton
                                    className="h-6 w-full rounded-full"
                                    animatePulse={false}
                                  />
                                </span>
                              </>
                            )}
                          </RadioGroup.Option>
                        </div>
                      </RadioGroup>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {mode === "success" && (
            <div className="flex">
              <div
                className={cn(
                  "mx-auto flex-1 pr-[380px]",
                  form?.pageMode === "compact" && "pt-20",
                  form?.pageMode === "full" && "pt-16",
                )}
              >
                <FormBuilderPreview form={form as FormByIdOutput} />
              </div>

              {/* Success Page Editor Sidebar */}
              <div className="fixed top-16 right-0 h-full w-[380px] overflow-y-auto border-l border-gray-200 bg-white">
                <div className="space-y-6 p-6 pb-24">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900">
                      Success Page
                    </h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Customize the text and button your users will see when
                      they submit a form.
                    </p>
                  </div>

                  {!hasAccess("Customize thank you page") && (
                    <div className="rounded-lg bg-blue-50 px-4 py-3 text-sm font-medium text-blue-700 ring-1 ring-blue-700/10 ring-inset">
                      <div className="flex items-center justify-between">
                        <p>Upgrade to access this feature</p>
                        <Link
                          href={`/dashboard/${orgId}/settings/subscription`}
                        >
                          <Button
                            variant="link"
                            size="sm"
                            className="text-blue-700"
                          >
                            Upgrade
                          </Button>
                        </Link>
                      </div>
                    </div>
                  )}

                  {/* Content Section */}
                  <div>
                    <h4 className="mb-4 text-sm font-semibold tracking-wide text-gray-800 uppercase">
                      Content
                    </h4>
                    <div className="space-y-4">
                      <Input
                        label="Heading"
                        value={form?.tpHeader || ""}
                        onChange={handleTPHeaderChange}
                        disabled={!hasAccess("Customize thank you page")}
                        placeholder="Thanks for completing this form!"
                      />
                      <Textarea
                        label="Message"
                        value={form?.tpMessage || ""}
                        onChange={handleTPMessageChange}
                        disabled={!hasAccess("Customize thank you page")}
                        placeholder="Made with Formbox, the easiest way to create forms for free."
                      />
                      <Input
                        label="Button text"
                        value={form?.tpButtonText || ""}
                        onChange={handleTPButtonTextChange}
                        disabled={!hasAccess("Customize thank you page")}
                        placeholder="Create your own form"
                      />
                      <Input
                        label="Button URL"
                        value={form?.tpButtonUrl || ""}
                        onChange={handleTPButtonUrlChange}
                        disabled={!hasAccess("Customize thank you page")}
                        placeholder="https://formbox.app"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      <ImageUploader
        open={headerUploaderOpen}
        onClose={headerUploaderHandler.close}
        submit={(url: string) => onUrlUpload(url, "headerImage")}
        onUpload={(file: File) => onFileUpload(file, "headerImage")}
      />

      <ImageUploader
        open={logoUploaderOpen}
        onClose={logoUploaderHandler.close}
        submit={(url: string) => onUrlUpload(url, "logo")}
        onUpload={(file: File) => onFileUpload(file, "logo")}
        showUnsplash={false}
      />
    </div>
  );
}
