import { useFieldActions } from "@/hooks/use-field-actions";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { IconPageBreak } from "@tabler/icons-react";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified page element using shared components.
 */
export function PageElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );

  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<IconPageBreak size={16} />}
          fieldTypeName="Page Break"
          labelPlaceholder="Page title"
          showDescriptionToggle={false}
          showRequiredToggle={false}
        >
          {/* Page breaks don't need additional content */}
        </EditMode>
      ) : (
        <ViewMode field={field} showDescription={false}>
          <div className="text-center">
            <h3 className="text-lg font-semibold">{field?.label}</h3>
          </div>
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
