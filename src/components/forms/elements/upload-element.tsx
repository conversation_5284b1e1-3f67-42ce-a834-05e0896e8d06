import { useFieldActions } from "@/hooks/use-field-actions";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { cn } from "@/utils/tailwind-helpers";
import { IconUpload } from "@tabler/icons-react";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified upload element using shared components.
 */
export function UploadElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );

  const isSelected = field.id === selectedId;

  const UploadArea = () => (
    <div
      className={cn(
        "flex h-[150px] w-full cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-900/25 py-10",
      )}
    >
      <div>
        <IconUpload className="h-6 w-6 text-gray-700" />
      </div>
      <p className="mt-4 text-base">
        Click to choose a file or drag image here
      </p>
      <p className="mt-4 text-sm text-gray-600">Size limit: 10MB</p>
    </div>
  );

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<IconUpload size={16} />}
          fieldTypeName="File Upload"
          labelPlaceholder="Enter a question"
        >
          <UploadArea />
        </EditMode>
      ) : (
        <ViewMode field={field}>
          <UploadArea />
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
