import { DatePicker } from "@/components/ui/date-picker";
import { useFieldActions } from "@/hooks/use-field-actions";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { Calendar } from "lucide-react";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified date element using shared components.
 */
export function DateElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );

  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<Calendar size={16} />}
          fieldTypeName="Date"
          labelPlaceholder="Enter a question"
        >
          <DatePicker className="pointer-events-none" />
        </EditMode>
      ) : (
        <ViewMode field={field}>
          <DatePicker
            label={field?.label}
            description={field?.description}
            classNames={{ label: "cursor-pointer" }}
            className="pointer-events-none"
          />
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
