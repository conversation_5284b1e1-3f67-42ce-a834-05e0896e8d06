import { Input } from "@/components/ui/input";
import { useFieldActions } from "@/hooks/use-field-actions";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { IconPhone } from "@tabler/icons-react";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified phone number element using shared components.
 */
export function PhoneNumberElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );

  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<IconPhone size={16} />}
          fieldTypeName="Phone Number"
          labelPlaceholder="Enter a question"
        >
          <Input placeholder="Recipient's answer goes here" disabled />
        </EditMode>
      ) : (
        <ViewMode field={field}>
          <Input
            label={field?.label || "Phone number"}
            placeholder="Recipient's answer goes here"
            description={field?.description}
            required={field?.required}
            className="pointer-events-none cursor-pointer"
            classNames={{ label: "cursor-pointer" }}
          />
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
