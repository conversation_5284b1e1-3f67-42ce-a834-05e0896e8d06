import { Rating } from "@/components/ui/Rating";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ToolTip } from "@/components/ui/tooltip";
import { useFieldActions } from "@/hooks/use-field-actions";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { IconSettings, IconStar } from "@tabler/icons-react";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified rating element using shared components.
 */
export function RatingElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );

  const isSelected = field.id === selectedId;

  const updateRating = (e: React.SyntheticEvent<HTMLInputElement>) => {
    const { value } = e.currentTarget;
    updateProperty("ratingCount", Number(value));
  };

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<IconStar size={16} />}
          fieldTypeName="Rating"
          labelPlaceholder="Enter a question"
        >
          <div>
            <Rating ratingCount={field.ratingCount} />
          </div>

          <div className="flex items-center space-x-2">
            <ToolTip message="Configuration">
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="icon">
                    <IconSettings size={16} />
                  </Button>
                </PopoverTrigger>
                <PopoverContent>
                  <Input
                    label="Stars"
                    type="number"
                    max={10}
                    defaultValue={field.ratingCount}
                    onChange={updateRating}
                  />
                </PopoverContent>
              </Popover>
            </ToolTip>
          </div>
        </EditMode>
      ) : (
        <ViewMode field={field}>
          <Rating ratingCount={field.ratingCount} />
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
