import { useFieldActions } from "@/hooks/use-field-actions";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { IconHeading } from "@tabler/icons-react";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified heading element using shared components.
 */
export function HeadingElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );
  const isSelected = field.id === selectedId;

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<IconHeading size={16} />}
          fieldTypeName="Heading"
          labelPlaceholder="Use this as a heading between sections of questions"
          descriptionMultiline={true}
          showRequiredToggle={false} // Headings don't need required toggle
        />
      ) : (
        <ViewMode field={field} showLabel={false} showDescription={false}>
          <div className="space-y-2">
            <h2 className="text-xl font-semibold">
              {field.label || "Section heading"}
            </h2>
            {field.showDescription && field.description && (
              <p className="text-gray-600">{field.description}</p>
            )}
          </div>
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}
