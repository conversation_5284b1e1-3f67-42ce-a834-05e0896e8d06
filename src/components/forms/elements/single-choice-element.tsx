import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useFieldActions } from "@/hooks/use-field-actions";
import { nanoid } from "@/libs/nanoid";
import { useFormStore } from "@/stores/form.store";
import { type FormField } from "@/types/form.types";
import { move } from "@dnd-kit/helpers";
import { DragDropProvider } from "@dnd-kit/react";
import { useSortable } from "@dnd-kit/react/sortable";
import {
  IconCircleCheck,
  IconGripVertical,
  IconPlus,
  IconSquareCheck,
  IconX,
} from "@tabler/icons-react";
import { useEffect, useRef, useState } from "react";
import { FormFieldContainer } from "../form-field-container";
import { EditMode } from "../shared/edit-mode";
import { ViewMode } from "../shared/view-mode";

interface Props {
  field: FormField;
  index?: number;
}

/**
 * Simplified single choice element using shared components.
 */
export function SingleChoiceElement({ field, index }: Props) {
  const { selectedId } = useFormStore();
  const { duplicate, remove, updateProperty, selectField } = useFieldActions(
    field.id,
  );
  const [lastAddedOptionId, setLastAddedOptionId] = useState<string | null>(
    null,
  );
  const optionRefs = useRef<Record<string, HTMLInputElement | null>>({});

  const isSelected = field.id === selectedId;

  const addOption = () => {
    const newOptionId = nanoid();
    const newOptions = [
      ...(field.options || []),
      { id: newOptionId, value: `Option ${(field.options?.length || 0) + 1}` },
    ];
    updateProperty("options", newOptions);
    setLastAddedOptionId(newOptionId);
  };

  const updateOption = (e: React.SyntheticEvent<HTMLInputElement>) => {
    const { id, value } = e.currentTarget;
    const updatedOptions = field.options?.map((option) => {
      if (id === option.id) {
        return { ...option, value: value.trim() };
      }
      return option;
    });
    updateProperty("options", updatedOptions);
  };

  const deleteOption = (id: string) => {
    const updatedOptions = field.options?.filter((option) => option.id !== id);
    updateProperty("options", updatedOptions);
  };

  const reorderOptions = (updatedOptions: typeof field.options) => {
    updateProperty("options", updatedOptions);
  };

  const convertToMultipleChoice = () => {
    updateProperty("type", "multiple-choice");
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addOption();
    } else if (
      (e.key === "Delete" || e.key === "Backspace") &&
      e.currentTarget.value === ""
    ) {
      e.preventDefault();
      deleteOption(e.currentTarget.id);

      // Focus the previous input if available
      const currentIndex =
        field.options?.findIndex((opt) => opt.id === e.currentTarget.id) || 0;
      if (currentIndex > 0 && field.options && field.options.length > 1) {
        const prevOptionId = field?.options?.[currentIndex - 1]?.id ?? "";
        setTimeout(() => {
          optionRefs.current[prevOptionId]?.focus();
        }, 0);
      }
    }
  };

  useEffect(() => {
    if (lastAddedOptionId && optionRefs.current[lastAddedOptionId]) {
      optionRefs.current[lastAddedOptionId]?.focus();
      setLastAddedOptionId(null);
    }
  }, [lastAddedOptionId, field.options]);

  return (
    <FormFieldContainer
      fieldId={field.id}
      index={index}
      selectedId={selectedId}
      setSelectedId={selectField}
    >
      {isSelected ? (
        <EditMode
          field={field}
          onUpdate={updateProperty}
          onDuplicate={duplicate}
          onDelete={remove}
          fieldIcon={<IconCircleCheck size={16} />}
          fieldTypeName="Single Choice"
          labelPlaceholder="Enter a question"
        >
          <div className="space-y-2">
            <DragDropProvider
              onDragEnd={(event) => {
                if (field.options) {
                  const updatedOptions = move(field.options, event);
                  reorderOptions(updatedOptions);
                }
              }}
            >
              {field.options?.map((option, index) => (
                <SingleOptionItem
                  key={option.id}
                  option={option}
                  index={index}
                  updateOption={updateOption}
                  deleteOption={deleteOption}
                  handleKeyDown={handleKeyDown}
                  optionRefs={optionRefs}
                />
              ))}
            </DragDropProvider>
          </div>

          <div className="flex space-x-2">
            <Button
              variant="outline"
              leftIcon={<IconPlus size={14} />}
              onClick={addOption}
              size="sm"
            >
              Add option
            </Button>
            <Button
              variant="secondary"
              leftIcon={<IconSquareCheck size={14} />}
              onClick={convertToMultipleChoice}
              size="sm"
            >
              Convert to multiple choice
            </Button>
          </div>
        </EditMode>
      ) : (
        <ViewMode field={field}>
          <div className="mt-[4px] space-y-2">
            {field.options?.map((option) => (
              <div className="flex items-center space-x-2" key={option.id}>
                <input
                  type="radio"
                  value=""
                  id={option.id}
                  disabled
                  className="accent-color size-[18px] cursor-pointer rounded-full border-gray-300 text-primary outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"
                />
                <label className="text-sm" htmlFor={option.id}>
                  {option.value}
                </label>
              </div>
            ))}
          </div>
        </ViewMode>
      )}
    </FormFieldContainer>
  );
}

// New component for sortable single choice option items
function SingleOptionItem({
  option,
  index,
  updateOption,
  deleteOption,
  handleKeyDown,
  optionRefs,
}: {
  option: { id: string; value: string };
  index: number;
  updateOption: (e: React.SyntheticEvent<HTMLInputElement>) => void;
  deleteOption: (id: string) => void;
  handleKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  optionRefs: React.RefObject<Record<string, HTMLInputElement | null>>;
}) {
  const { ref, isDragging } = useSortable({ id: option.id, index });

  return (
    <div
      ref={ref}
      className={`flex w-full items-center ${isDragging ? "opacity-50" : ""}`}
    >
      <div className="flex min-w-[38px] items-center gap-1">
        <span className="flex h-[20px] w-[20px] cursor-grab items-center justify-center text-gray-700">
          <IconGripVertical size={16} />
        </span>
        <input
          type="radio"
          disabled
          className="accent-color size-[18px] cursor-pointer rounded-full border-gray-300 text-primary outline-hidden focus:ring-0 focus:ring-offset-0 focus:outline-hidden focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2"
          style={{ marginLeft: 0 }}
        />
      </div>
      <div className="ml-3 w-full">
        <Input
          id={option.id}
          placeholder="Enter an option"
          defaultValue={option.value}
          onChange={updateOption}
          onKeyDown={handleKeyDown}
          ref={(el) => {
            optionRefs.current[option.id] = el;
          }}
        />
      </div>
      <Button
        className="ml-3 h-9 w-10"
        variant="outline"
        onClick={() => deleteOption(option.id)}
        size="icon"
      >
        <IconX size={16} />
      </Button>
    </div>
  );
}
