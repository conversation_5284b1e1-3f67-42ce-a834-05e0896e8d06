import { ELEMENT_COMPONENTS } from "@/components/forms/elements/element-map";
import { Card } from "@/components/ui/card";
import { BubbleEditor } from "@/components/ui/rich-text-editor-bubble";
import type { FormByIdOutput } from "@/types/form.types";
import { move } from "@dnd-kit/helpers";
import { DragDropProvider } from "@dnd-kit/react";
import { IconAt, IconPlus } from "@tabler/icons-react";
import { isEmpty } from "radash";
import { CgDetailsLess } from "react-icons/cg";
import TextareaAutosize from "react-textarea-autosize";

interface FormCanvasProps {
  form: FormByIdOutput;
  onFormUpdate: (form: FormByIdOutput) => void;
  onAddShortAnswer: () => void;
  onAddEmail: () => void;
  targetRef: React.RefObject<HTMLDivElement | null>;
}

export function FormBuilderCanvas({
  form,
  onFormUpdate,
  onAddShortAnswer,
  onAddEmail,
  targetRef,
}: FormCanvasProps) {
  const updateFormHeaderTitle = (
    e: React.SyntheticEvent<HTMLTextAreaElement>,
  ) => {
    const { value } = e.currentTarget;
    onFormUpdate({ ...form, headerTitle: value });
  };

  const updateFormHeaderDescription = (content: string) => {
    onFormUpdate({ ...form, headerDescription: content });
  };

  return (
    <div className="mx-auto max-w-3xl space-y-4 px-5">
      {/* Form Heading Title and Description */}
      <Card className="w-full border-gray-300">
        <div className="w-full p-5">
          <div className="flex w-full flex-col">
            <TextareaAutosize
              value={form?.headerTitle || ""}
              className="resize-none border-none text-3xl font-semibold focus:ring-0"
              placeholder="Form title"
              onChange={updateFormHeaderTitle}
              rows={1}
            />
            <BubbleEditor
              key={`form-description-${form.id}`}
              onContentUpdate={updateFormHeaderDescription}
              defaultContent={form.headerDescription}
              inputClassName="py-[8px] px-[12px] prose max-w-none prose-sm sm:prose prose-li:marker:text-gray-900 prose-ul:marker:text-gray-900 prose-p:m-0 prose-headings:m-0 prose-a:text-blue-600 prose-a:no-underline hover:prose-a:underline"
              placeholder="Form description (optional)"
            />
          </div>
        </div>
      </Card>

      {/* Form Fields */}
      {!isEmpty(form?.fields) && (
        <DragDropProvider
          onDragEnd={(event) => {
            const updatedFields = move(form.fields, event);
            onFormUpdate({
              ...form,
              fields: updatedFields,
            });
          }}
        >
          <div className="mt-4 space-y-4">
            {form?.fields?.map((field, index) => {
              const ElementComponent =
                ELEMENT_COMPONENTS[
                  field.subtype as keyof typeof ELEMENT_COMPONENTS
                ];

              if (!ElementComponent) {
                return (
                  <div
                    key={field.id}
                    className="rounded-lg border border-red-200 bg-red-50 p-4"
                  >
                    <p className="text-sm text-red-600">
                      Element type &quot;{field.subtype}&quot; not found in
                      element mapping.
                    </p>
                  </div>
                );
              }

              // All elements now use simplified props - no more legacy handling needed
              return (
                <ElementComponent key={field.id} field={field} index={index} />
              );
            })}
          </div>
          <div ref={targetRef}></div>
        </DragDropProvider>
      )}

      {/* Empty State */}
      {isEmpty(form?.fields) && (
        <div className="flex flex-col items-center justify-center px-8 py-24">
          <div className="mb-6 rounded-full bg-linear-to-br from-blue-50 to-indigo-100 p-6">
            <div className="rounded-full bg-linear-to-br from-blue-100 to-indigo-200 p-4">
              <IconPlus size={32} className="text-blue-600" />
            </div>
          </div>
          <h3 className="mb-2 text-xl font-semibold text-gray-900">
            Start building your form
          </h3>
          <p className="mb-8 max-w-md text-center text-gray-500">
            Add your first element to get started. Choose from text fields,
            multiple choice questions, and more from the panel on the right.
          </p>
          <div className="flex flex-col gap-3 sm:flex-row">
            <button
              onClick={onAddShortAnswer}
              className="inline-flex items-center rounded-lg border border-blue-200 bg-blue-50 px-4 py-2 text-sm font-medium text-blue-700 transition-colors hover:border-blue-300 hover:bg-blue-100"
            >
              <CgDetailsLess size={16} className="mr-2" />
              Add Short Answer
            </button>
            <button
              onClick={onAddEmail}
              className="inline-flex items-center rounded-lg border border-green-200 bg-green-50 px-4 py-2 text-sm font-medium text-green-700 transition-colors hover:border-green-300 hover:bg-green-100"
            >
              <IconAt size={16} className="mr-2" />
              Add Email Field
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
